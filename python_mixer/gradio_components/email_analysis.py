"""
Email Analysis Component for Fulmark.pl HVAC CRM Interface
==========================================================

Advanced email processing and analysis interface with AI-powered insights
specifically designed for HVAC customer communications.

Features:
- Intelligent email parsing and classification
- Sentiment analysis for customer satisfaction
- Equipment and service request detection
- Priority assessment and urgency classification
- Customer profile enrichment
- Automated response suggestions
"""

import gradio as gr
import json
import time
import plotly.graph_objects as go
from datetime import datetime
from typing import Dict, List, Any, Tuple
from loguru import logger

from .base_styles import BaseStyles


class EmailAnalysisComponent:
    """
    Email analysis component for HVAC CRM operations.
    
    Provides comprehensive email processing capabilities with AI-powered insights
    optimized for Fulmark.pl customer communications.
    """
    
    def __init__(self):
        self.styles = BaseStyles()
        self.analysis_history = []
        
    def create_interface(self) -> gr.Column:
        """
        Create the email analysis interface.
        
        Returns:
            gr.Column: Complete email analysis interface
        """
        with gr.Column() as interface:
            # Header and description
            gr.Markdown("""
            ### 📧 Inteligentna Analiza Emaili - Fulmark.pl
            
            **Zaawansowana analiza komunikacji z klientami:**
            - 🚀 **4x Szybsze rozpoznawanie** kluczowych elementów
            - 🎯 **Klasyfikacja intencji** - serwis, oferta, reklamacja
            - 🧠 **Analiza sentymentu** dla oceny satysfakcji klienta
            - ⚡ **Ocena pilności** z automatycznym priorytetem
            - 🔧 **Rozpoznawanie sprzętu** LG, Daikin, inne marki
            - 💡 **Sugestie odpowiedzi** dopasowane do sytuacji
            """)
            
            with gr.Row():
                with gr.Column(scale=2):
                    # Email input section
                    gr.Markdown("#### 📝 Treść Emaila")
                    
                    email_input = gr.Textbox(
                        label="Treść emaila od klienta",
                        placeholder="Wklej tutaj email od klienta...\n\nPrzykład:\nOd: <EMAIL>\nTemat: Problem z klimatyzacją LG\n\nDzień dobry, mam problem z klimatyzacją...",
                        lines=12,
                        max_lines=20,
                        **self.styles.get_component_styles()["input_enhanced"]
                    )
                    
                    # Quick templates
                    with gr.Accordion("📋 Szybkie Szablony Testowe", open=False):
                        gr.Markdown("**Kliknij aby wstawić przykładowe emaile:**")
                        with gr.Row():
                            template_service = gr.Button(
                                "🔧 Zgłoszenie Serwisowe",
                                **self.styles.get_component_styles()["button_secondary"]
                            )
                            template_quote = gr.Button(
                                "💰 Zapytanie o Ofertę", 
                                **self.styles.get_component_styles()["button_secondary"]
                            )
                            template_complaint = gr.Button(
                                "😠 Reklamacja",
                                **self.styles.get_component_styles()["button_secondary"]
                            )
                    
                    # Analysis options
                    with gr.Row():
                        with gr.Column():
                            framework_choice = gr.Radio(
                                choices=["🧠 LangGraph (Zalecane)", "🤖 CrewAI", "🔄 OpenAI Swarm"],
                                value="🧠 LangGraph (Zalecane)",
                                label="Framework AI",
                                info="Wybierz framework do analizy"
                            )
                        
                        with gr.Column():
                            with gr.Accordion("⚙️ Opcje Analizy", open=True):
                                include_sentiment = gr.Checkbox(
                                    label="😊 Analiza Sentymentu",
                                    value=True,
                                    info="Oceń emocje i satysfakcję klienta"
                                )
                                include_entities = gr.Checkbox(
                                    label="🏷️ Rozpoznawanie Elementów",
                                    value=True,
                                    info="Wyodrębnij sprzęt, lokalizacje, dane"
                                )
                                include_intent = gr.Checkbox(
                                    label="🎯 Klasyfikacja Intencji",
                                    value=True,
                                    info="Określ cel klienta"
                                )
                                include_priority = gr.Checkbox(
                                    label="⚡ Ocena Pilności",
                                    value=True,
                                    info="Oceń pilność i ważność"
                                )
                    
                    # Action buttons
                    with gr.Row():
                        analyze_btn = gr.Button(
                            "🔍 Analizuj Email",
                            **self.styles.get_component_styles()["button_primary"]
                        )
                        clear_btn = gr.Button(
                            "🗑️ Wyczyść",
                            **self.styles.get_component_styles()["button_secondary"]
                        )
                
                with gr.Column(scale=3):
                    # Analysis status
                    analysis_status = gr.HTML(
                        value=self._get_ready_status(),
                        label="Status Analizy"
                    )
                    
                    # Results tabs
                    with gr.Tabs():
                        with gr.Tab("📊 Wyniki Analizy"):
                            analysis_results = gr.JSON(
                                label="Szczegółowe Wyniki Analizy",
                                value={},
                                **self.styles.get_component_styles()["card_metric"]
                            )
                        
                        with gr.Tab("👤 Profil Klienta"):
                            customer_insights = gr.Markdown(
                                value="Insights o kliencie pojawią się tutaj po analizie...",
                                **self.styles.get_component_styles()["card_customer"]
                            )
                        
                        with gr.Tab("🎯 Rekomendacje"):
                            recommended_actions = gr.Markdown(
                                value="Rekomendacje AI pojawią się tutaj...",
                                **self.styles.get_component_styles()["card_metric"]
                            )
                        
                        with gr.Tab("📈 Metryki Wizualne"):
                            metrics_plot = gr.Plot(
                                label="Wizualizacja Metryk Analizy",
                                value=self._create_empty_metrics_plot(),
                                **self.styles.get_component_styles()["card_metric"]
                            )
            
            # Event handlers
            analyze_btn.click(
                fn=self._analyze_email_enhanced,
                inputs=[email_input, framework_choice, include_sentiment, include_entities, include_intent, include_priority],
                outputs=[analysis_status, analysis_results, customer_insights, recommended_actions, metrics_plot]
            )
            
            clear_btn.click(
                fn=lambda: ("", {}, "Gotowy do nowej analizy...", "Brak rekomendacji...", self._create_empty_metrics_plot()),
                outputs=[email_input, analysis_results, customer_insights, recommended_actions, metrics_plot]
            )
            
            # Template handlers
            template_service.click(
                fn=lambda: self._get_email_template("service"),
                outputs=[email_input]
            )
            template_quote.click(
                fn=lambda: self._get_email_template("quote"),
                outputs=[email_input]
            )
            template_complaint.click(
                fn=lambda: self._get_email_template("complaint"),
                outputs=[email_input]
            )
        
        return interface
    
    def _get_ready_status(self) -> str:
        """Get ready status HTML."""
        return """
        <div class="metric-card">
            <h4 style="margin: 0 0 12px 0; color: #1a73e8;">🎯 Status Analizy</h4>
            <p style="margin: 0; color: #666;">Gotowy do analizy emaili klientów z inteligencją AI</p>
            <div style="margin-top: 12px; padding: 8px; background: #e3f2fd; border-radius: 8px; font-size: 0.9em;">
                💡 <strong>Wskazówka:</strong> Użyj szablonów poniżej do szybkiego testowania systemu
            </div>
        </div>
        """
    
    def _create_empty_metrics_plot(self) -> go.Figure:
        """Create empty metrics plot."""
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=[datetime.now()],
            y=[0],
            mode='markers',
            name='Metryki',
            marker=dict(size=12, color='#1a73e8')
        ))
        fig.update_layout(
            title="📊 Metryki Analizy Emaili",
            xaxis_title="Czas",
            yaxis_title="Wartość",
            template="plotly_white",
            height=400,
            font=dict(family="Google Sans, Roboto, sans-serif"),
            title_font=dict(size=16, color="#1a73e8"),
            plot_bgcolor='rgba(248,249,255,0.8)',
            paper_bgcolor='rgba(255,255,255,0.9)'
        )
        return fig
    
    def _get_email_template(self, template_type: str) -> str:
        """Get email template by type."""
        templates = {
            "service": """Od: <EMAIL>
Do: <EMAIL>
Temat: 🔧 Awaria klimatyzacji LG - pilne wsparcie
Data: Pon, 1 Sty 2024 10:00:00 +0100

Dzień dobry,

Mam pilny problem z klimatyzacją LG model S12ET w moim biurze.
Urządzenie przestało chłodzić i wyświetla błąd E1 na panelu.

Szczegóły:
- Model: LG S12ET Dual Cool
- Rok instalacji: 2022
- Lokalizacja: Warszawa, ul. Testowa 123
- Problem: Brak chłodzenia + kod błędu E1
- Pilność: Wysoka (upały, biuro pełne ludzi)

Proszę o pilny kontakt w sprawie serwisu.
Telefon: +48 123 456 789

Pozdrawiam,
Jan Kowalski
Kierownik Biura""",

            "quote": """Od: <EMAIL>
Do: <EMAIL>
Temat: 💰 Zapytanie o ofertę - klimatyzacja nowego biura
Data: Pon, 1 Sty 2024 14:00:00 +0100

Dzień dobry,

Prosimy o przygotowanie oferty na klimatyzację nowego biura:

Parametry lokalu:
- Powierzchnia: 120 m²
- 4 pomieszczenia (2 biura, sala konferencyjna, recepcja)
- Budynek biurowy w centrum Warszawy
- Wysokość sufitów: 3.2m
- Okna południowe (duże nasłonecznienie)

Wymagania:
- Preferujemy systemy LG lub Daikin
- Sterowanie WiFi w każdym pomieszczeniu
- Cicha praca (środowisko biurowe)
- Budżet: do 25 000 PLN

Proszę o kontakt w celu umówienia wizji lokalnej.
Termin realizacji: do końca marca 2024

Pozdrawiam,
Anna Nowak
Kierownik Administracji
Tel: +48 987 654 321""",

            "complaint": """Od: <EMAIL>
Do: <EMAIL>
Temat: 😠 REKLAMACJA - wadliwa instalacja klimatyzacji
Data: Pon, 1 Sty 2024 16:00:00 +0100

Dzień dobry,

Zgłaszam oficjalną reklamację instalacji klimatyzacji wykonanej 2 tygodnie temu.

Problemy z instalacją:
1. Głośna praca jednostki zewnętrznej (ponad 65dB)
2. Nierównomierne chłodzenie pomieszczeń
3. Przecieki z jednostki wewnętrznej w sypialni
4. Uszkodzenia ściany podczas montażu (nieuzgodnione)

Dane instalacji:
- Model: LG Dual Cool S18ET
- Adres: ul. Domowa 456, Warszawa
- Data instalacji: 15.12.2023
- Numer faktury: FV/2023/1234
- Technik: Pan Kowalski

Żądam:
- Natychmiastowej naprawy wszystkich usterek
- Rekompensaty za uszkodzenia ściany
- Przedłużenia gwarancji o okres przestoju

Proszę o pilny kontakt i naprawę w ciągu 48h.
W przeciwnym razie skieruję sprawę do UOKiK.

Piotr Kowalczyk
Tel: +48 987 654 321
Email: <EMAIL>"""
        }
        return templates.get(template_type, "")
    
    def _analyze_email_enhanced(
        self,
        email_content: str,
        framework: str,
        include_sentiment: bool,
        include_entities: bool,
        include_intent: bool,
        include_priority: bool
    ) -> Tuple[str, Dict, str, str, go.Figure]:
        """Enhanced email analysis with AI processing."""
        try:
            if not email_content.strip():
                return (
                    """
                    <div class="status-warning">
                        <h4 style="margin: 0 0 12px 0;">⚠️ Brak Treści do Analizy</h4>
                        <p style="margin: 0;">Proszę wprowadzić treść emaila lub użyć jednego z szablonów.</p>
                    </div>
                    """,
                    {},
                    "Proszę wprowadzić treść emaila do analizy...",
                    "Brak rekomendacji bez treści...",
                    self._create_empty_metrics_plot()
                )
            
            # Simulate processing time
            start_time = time.time()
            
            # Processing status
            status_html = """
            <div class="status-success">
                <h4 style="margin: 0 0 12px 0;">🔄 Przetwarzanie...</h4>
                <p style="margin: 0;">Analizuję email z wykorzystaniem AI...</p>
                <div class="progress-ring" style="margin: 12px auto;"></div>
            </div>
            """
            
            # Simulate AI analysis (replace with real implementation)
            analysis_result = self._simulate_ai_analysis(email_content, framework, include_sentiment, include_entities, include_intent, include_priority)
            
            processing_time = time.time() - start_time
            
            # Success status
            success_status = f"""
            <div class="status-success">
                <h4 style="margin: 0 0 12px 0;">✅ Analiza Zakończona</h4>
                <p style="margin: 0;"><strong>Czas przetwarzania:</strong> {processing_time:.2f}s</p>
                <p style="margin: 8px 0 0 0;"><strong>Framework:</strong> {framework}</p>
                <p style="margin: 8px 0 0 0;"><strong>Elementy analizy:</strong> {sum([include_sentiment, include_entities, include_intent, include_priority])}/4</p>
            </div>
            """
            
            # Generate insights and recommendations
            customer_insights = self._generate_customer_insights(analysis_result)
            recommendations = self._generate_recommendations(analysis_result)
            metrics_plot = self._create_analysis_metrics_plot(analysis_result)
            
            return success_status, analysis_result, customer_insights, recommendations, metrics_plot
            
        except Exception as e:
            logger.error(f"Email analysis error: {e}")
            return (
                f"""
                <div class="status-error">
                    <h4 style="margin: 0 0 12px 0;">❌ Błąd Analizy</h4>
                    <p style="margin: 0;">Wystąpił błąd: {str(e)}</p>
                </div>
                """,
                {},
                "Błąd podczas analizy...",
                "Nie można wygenerować rekomendacji...",
                self._create_empty_metrics_plot()
            )

    def _simulate_ai_analysis(self, email_content: str, framework: str, include_sentiment: bool, include_entities: bool, include_intent: bool, include_priority: bool) -> Dict:
        """Simulate AI analysis of email content."""
        # Extract basic information
        lines = email_content.split('\n')
        subject_line = next((line for line in lines if line.startswith('Temat:') or line.startswith('Subject:')), "")
        from_line = next((line for line in lines if line.startswith('Od:') or line.startswith('From:')), "")

        # Simulate analysis results
        result = {
            "email_metadata": {
                "from": from_line.split(':', 1)[1].strip() if ':' in from_line else "<EMAIL>",
                "subject": subject_line.split(':', 1)[1].strip() if ':' in subject_line else "Brak tematu",
                "timestamp": datetime.now().isoformat(),
                "framework_used": framework,
                "processing_time_ms": 850
            }
        }

        if include_sentiment:
            # Determine sentiment based on keywords
            content_lower = email_content.lower()
            if any(word in content_lower for word in ['pilne', 'awaria', 'problem', 'błąd', 'reklamacja', 'nie działa']):
                sentiment = {"score": -0.6, "label": "Negatywny", "confidence": 0.85}
            elif any(word in content_lower for word in ['dziękuję', 'świetnie', 'zadowolony', 'polecam']):
                sentiment = {"score": 0.7, "label": "Pozytywny", "confidence": 0.90}
            else:
                sentiment = {"score": 0.1, "label": "Neutralny", "confidence": 0.75}

            result["sentiment_analysis"] = sentiment

        if include_entities:
            # Extract entities
            entities = []
            content_lower = email_content.lower()

            # Equipment detection
            if 'lg' in content_lower:
                entities.append({"type": "EQUIPMENT", "value": "LG", "confidence": 0.95})
            if 'daikin' in content_lower:
                entities.append({"type": "EQUIPMENT", "value": "Daikin", "confidence": 0.95})
            if 's12et' in content_lower:
                entities.append({"type": "MODEL", "value": "S12ET", "confidence": 0.90})

            # Location detection
            if 'warszawa' in content_lower:
                entities.append({"type": "LOCATION", "value": "Warszawa", "confidence": 0.85})

            # Phone detection
            import re
            phone_pattern = r'\+?48\s?\d{3}\s?\d{3}\s?\d{3}'
            phones = re.findall(phone_pattern, email_content)
            for phone in phones:
                entities.append({"type": "PHONE", "value": phone, "confidence": 0.95})

            result["entities"] = entities

        if include_intent:
            # Classify intent
            content_lower = email_content.lower()
            if any(word in content_lower for word in ['serwis', 'naprawa', 'awaria', 'problem', 'błąd']):
                intent = {"category": "SERVICE_REQUEST", "confidence": 0.90, "description": "Zgłoszenie serwisowe"}
            elif any(word in content_lower for word in ['oferta', 'wycena', 'cena', 'koszt', 'zapytanie']):
                intent = {"category": "QUOTE_REQUEST", "confidence": 0.85, "description": "Zapytanie o ofertę"}
            elif any(word in content_lower for word in ['reklamacja', 'skarga', 'niezadowolony', 'uokik']):
                intent = {"category": "COMPLAINT", "confidence": 0.95, "description": "Reklamacja/skarga"}
            else:
                intent = {"category": "GENERAL_INQUIRY", "confidence": 0.70, "description": "Ogólne zapytanie"}

            result["intent_classification"] = intent

        if include_priority:
            # Assess priority
            content_lower = email_content.lower()
            if any(word in content_lower for word in ['pilne', 'natychmiast', 'awaria', 'nie działa', 'upały']):
                priority = {"level": "HIGH", "score": 0.9, "description": "Wysoki priorytet - wymaga szybkiej reakcji"}
            elif any(word in content_lower for word in ['reklamacja', 'uokik', 'prawnik']):
                priority = {"level": "CRITICAL", "score": 1.0, "description": "Krytyczny - potencjalne problemy prawne"}
            elif any(word in content_lower for word in ['oferta', 'wycena', 'planujemy']):
                priority = {"level": "MEDIUM", "score": 0.6, "description": "Średni priorytet - potencjalna sprzedaż"}
            else:
                priority = {"level": "LOW", "score": 0.3, "description": "Niski priorytet - standardowe zapytanie"}

            result["priority_assessment"] = priority

        return result

    def _generate_customer_insights(self, analysis_result: Dict) -> str:
        """Generate customer insights based on analysis."""
        insights = ["### 👤 Profil Klienta - Fulmark.pl\n"]

        metadata = analysis_result.get("email_metadata", {})
        sentiment = analysis_result.get("sentiment_analysis", {})
        entities = analysis_result.get("entities", [])
        intent = analysis_result.get("intent_classification", {})
        priority = analysis_result.get("priority_assessment", {})

        # Basic info
        insights.append(f"**📧 Email:** {metadata.get('from', 'Nieznany')}")
        insights.append(f"**📅 Data kontaktu:** {datetime.now().strftime('%d.%m.%Y %H:%M')}")

        # Sentiment analysis
        if sentiment:
            emoji = "😊" if sentiment["score"] > 0.3 else "😐" if sentiment["score"] > -0.3 else "😠"
            insights.append(f"**{emoji} Nastrój klienta:** {sentiment['label']} ({sentiment['confidence']*100:.0f}% pewności)")

        # Equipment info
        equipment = [e["value"] for e in entities if e["type"] in ["EQUIPMENT", "MODEL"]]
        if equipment:
            insights.append(f"**🔧 Sprzęt:** {', '.join(equipment)}")

        # Location
        locations = [e["value"] for e in entities if e["type"] == "LOCATION"]
        if locations:
            insights.append(f"**📍 Lokalizacja:** {', '.join(locations)}")

        # Contact info
        phones = [e["value"] for e in entities if e["type"] == "PHONE"]
        if phones:
            insights.append(f"**📞 Telefon:** {phones[0]}")

        # Intent and priority
        if intent:
            insights.append(f"**🎯 Cel kontaktu:** {intent['description']}")

        if priority:
            priority_emoji = "🔴" if priority["level"] == "CRITICAL" else "🟠" if priority["level"] == "HIGH" else "🟡" if priority["level"] == "MEDIUM" else "🟢"
            insights.append(f"**{priority_emoji} Priorytet:** {priority['description']}")

        # Recommendations
        insights.append("\n#### 💡 Rekomendacje CRM:")
        if priority and priority["level"] in ["CRITICAL", "HIGH"]:
            insights.append("- ⚡ **Pilny kontakt** - zadzwoń w ciągu 30 minut")
            insights.append("- 📋 **Przypisz technika** specjalizującego się w danej marce")

        if intent and intent["category"] == "QUOTE_REQUEST":
            insights.append("- 💰 **Przygotuj ofertę** w ciągu 24h")
            insights.append("- 📊 **Zaplanuj wizję lokalną** dla dokładnej wyceny")

        return "\n".join(insights)

    def _generate_recommendations(self, analysis_result: Dict) -> str:
        """Generate action recommendations."""
        recommendations = ["### 🎯 Rekomendowane Działania - Fulmark.pl\n"]

        intent = analysis_result.get("intent_classification", {})
        priority = analysis_result.get("priority_assessment", {})
        sentiment = analysis_result.get("sentiment_analysis", {})
        entities = analysis_result.get("entities", [])

        # Priority-based actions
        if priority:
            if priority["level"] == "CRITICAL":
                recommendations.extend([
                    "#### 🚨 DZIAŁANIA NATYCHMIASTOWE:",
                    "1. **Kontakt telefoniczny w ciągu 15 minut**",
                    "2. **Eskalacja do kierownika serwisu**",
                    "3. **Dokumentacja dla działu prawnego**",
                    "4. **Przygotowanie planu naprawczego**"
                ])
            elif priority["level"] == "HIGH":
                recommendations.extend([
                    "#### ⚡ DZIAŁANIA PILNE:",
                    "1. **Kontakt telefoniczny w ciągu 30 minut**",
                    "2. **Przypisanie technika w ciągu 2h**",
                    "3. **Przygotowanie części zamiennych**",
                    "4. **Aktualizacja statusu w CRM**"
                ])

        # Intent-based actions
        if intent:
            if intent["category"] == "SERVICE_REQUEST":
                recommendations.extend([
                    "\n#### 🔧 DZIAŁANIA SERWISOWE:",
                    "- Sprawdź historię serwisową klienta",
                    "- Przygotuj narzędzia diagnostyczne",
                    "- Zarezerwuj części zamienne",
                    "- Zaplanuj wizytę technika"
                ])
            elif intent["category"] == "QUOTE_REQUEST":
                recommendations.extend([
                    "\n#### 💰 DZIAŁANIA SPRZEDAŻOWE:",
                    "- Przygotuj szczegółową ofertę",
                    "- Zaplanuj wizję lokalną",
                    "- Sprawdź dostępność sprzętu",
                    "- Przygotuj materiały marketingowe"
                ])
            elif intent["category"] == "COMPLAINT":
                recommendations.extend([
                    "\n#### 😠 DZIAŁANIA REKLAMACYJNE:",
                    "- Przeprowadź szczegółową analizę problemu",
                    "- Przygotuj plan naprawczy",
                    "- Rozważ rekompensatę",
                    "- Dokumentuj wszystkie działania"
                ])

        # Equipment-specific recommendations
        equipment = [e["value"] for e in entities if e["type"] in ["EQUIPMENT", "MODEL"]]
        if equipment:
            recommendations.extend([
                f"\n#### 🔧 REKOMENDACJE DLA SPRZĘTU {', '.join(equipment)}:",
                "- Sprawdź dostępność części zamiennych",
                "- Przygotuj dokumentację techniczną",
                "- Skontaktuj się z autoryzowanym serwisem",
                "- Sprawdź status gwarancji"
            ])

        # Follow-up actions
        recommendations.extend([
            "\n#### 📋 DZIAŁANIA NASTĘPNE:",
            "- Zaktualizuj profil klienta w CRM",
            "- Zaplanuj follow-up w odpowiednim czasie",
            "- Dodaj notatki do historii kontaktów",
            "- Oceń satysfakcję klienta po realizacji"
        ])

        return "\n".join(recommendations)

    def _create_analysis_metrics_plot(self, analysis_result: Dict) -> go.Figure:
        """Create metrics visualization plot."""
        fig = go.Figure()

        # Sentiment score
        sentiment = analysis_result.get("sentiment_analysis", {})
        if sentiment:
            fig.add_trace(go.Bar(
                x=['Sentiment'],
                y=[sentiment.get("score", 0)],
                name='Wynik Sentymentu',
                marker_color='#1a73e8'
            ))

        # Priority score
        priority = analysis_result.get("priority_assessment", {})
        if priority:
            fig.add_trace(go.Bar(
                x=['Priorytet'],
                y=[priority.get("score", 0)],
                name='Poziom Priorytetu',
                marker_color='#ff9800'
            ))

        # Confidence scores
        intent = analysis_result.get("intent_classification", {})
        if intent:
            fig.add_trace(go.Bar(
                x=['Pewność Intencji'],
                y=[intent.get("confidence", 0)],
                name='Pewność Klasyfikacji',
                marker_color='#4caf50'
            ))

        fig.update_layout(
            title="📊 Metryki Analizy Emaila",
            xaxis_title="Kategorie",
            yaxis_title="Wynik (0-1)",
            template="plotly_white",
            height=400,
            font=dict(family="Google Sans, Roboto, sans-serif"),
            title_font=dict(size=16, color="#1a73e8"),
            plot_bgcolor='rgba(248,249,255,0.8)',
            paper_bgcolor='rgba(255,255,255,0.9)',
            showlegend=True
        )

        return fig
